# reports/urls.py

from django.urls import path
from . import views
from .views import (
    detailed_financial_report,
    case_progress_report,
    inventory_management_report,
    dentist_performance_report,
    revenue_report,
    dentist_financial_report,
    DentistFinancialSummaryView
)

urlpatterns = [
    # Enhanced Dashboard
    path('', views.reports_dashboard, name='reports_dashboard'),

    # Existing Reports
    path('cycletimes/', views.cycle_time_report, name='cycletimes'),
    path('revenue-report/', revenue_report, name='revenue_report'),
    path('reports/case-volume/', views.case_volume_report, name='case_volume_report'),
    path('reports/case-status/', views.case_status_report, name='case_status_report'),
    path('reports/dentist-leaderboard/', views.dentist_leaderboard, name='dentist_leaderboard'),
    path('reports/appointment-scheduling/', views.appointment_scheduling_report, name='appointment_scheduling_report'),
    path('export/excel/', views.export_to_excel, name='export_to_excel'),
    path('export/pdf/', views.export_to_pdf, name='export_to_pdf'),
    path('reports/item_usage_report/', views.item_usage_report, name='item_usage_report'),
    path('reports/item-inventory/', views.item_inventory_report, name='item_inventory_report'),
    path('financial-report/', views.financial_report, name='financial_report'),
    path('reports/financial/', detailed_financial_report, name='detailed_financial_report'),
    path('reports/case-progress/', case_progress_report, name='case_progress_report'),
    path('reports/inventory/', inventory_management_report, name='inventory_management_report'),
    path('reports/dentist-performance/', dentist_performance_report, name='dentist_performance_report'),
    path('reports/dentist-financials/', dentist_financial_report, name='dentist_financial_report'),
    path('reports/dentist-summary/', DentistFinancialSummaryView.as_view(), name='dentist_financial_summary'),
    path('dentist_treemap_chart/', views.dentist_treemap_chart, name='dentist_treemap_chart'),

    # New Enhanced Reports
    path('reports/profitability-analysis/', views.profitability_analysis_report, name='profitability_analysis_report'),
    path('reports/cash-flow-forecast/', views.cash_flow_forecast_report, name='cash_flow_forecast_report'),
    path('reports/quality-metrics/', views.quality_metrics_dashboard, name='quality_metrics_dashboard'),
    path('reports/workflow-efficiency/', views.workflow_efficiency_analysis, name='workflow_efficiency_analysis'),
    path('reports/inventory-optimization/', views.inventory_optimization_report, name='inventory_optimization_report'),
    path('reports/demand-forecasting/', views.demand_forecasting_report, name='demand_forecasting_report'),
    path('reports/customer-relationship/', views.customer_relationship_analysis, name='customer_relationship_analysis'),
]
